import React, { useState } from 'react'
import { Search, Plus, Upload, RotateCcw } from 'lucide-react'
import EditDataModal from './EditDataModal'

const DataManagement = () => {
  const [showEditModal, setShowEditModal] = useState(false)
  const [selectedEntry, setSelectedEntry] = useState(null)
  const [formData, setFormData] = useState({
    managementSystem: 'District',
    district: 'Mardan',
    totalChildren: '12,000',
    outOfSchoolChildren: '4,500',
    gender: { girls: '55', boys: '45' },
    programType: 'Voucher',
    date: '04/08/2024',
    dropOutReasons: {
      poverty: true,
      distance: false,
      other: false
    }
  })

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleGenderChange = (type, value) => {
    setFormData(prev => ({
      ...prev,
      gender: {
        ...prev.gender,
        [type]: value
      }
    }))
  }

  const handleDropOutChange = (reason, checked) => {
    setFormData(prev => ({
      ...prev,
      dropOutReasons: {
        ...prev.dropOutReasons,
        [reason]: checked
      }
    }))
  }

  const handleAddEntry = () => {
    // Add entry logic here
    console.log('Adding entry:', formData)
  }

  const handleReset = () => {
    setFormData({
      managementSystem: 'District',
      district: '',
      totalChildren: '',
      outOfSchoolChildren: '',
      gender: { girls: '', boys: '' },
      programType: 'Voucher',
      date: '',
      dropOutReasons: {
        poverty: false,
        distance: false,
        other: false
      }
    })
  }

  const handleUploadCSV = () => {
    // CSV upload logic here
    console.log('Uploading CSV')
  }

  return (
    <div className="p-4 md:p-6 bg-[#F8F9FA] min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-4 md:mb-6">
          <h1 className="text-xl md:text-2xl font-semibold text-gray-900 mb-2">Data Management</h1>
        </div>

        {/* Main Content Card */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4 md:p-6">
          {/* Form Header */}
          <div className="flex flex-col md:flex-row md:items-center justify-between mb-4 md:mb-6 space-y-4 md:space-y-0">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center border">
                <span className="text-[#2c5aa0] font-bold text-sm">O</span>
              </div>
              <span className="font-medium text-sm">OOSC Edu App Track</span>
            </div>
            <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <span>Data Management</span>
              </div>
              <div className="flex items-center space-x-2">
                <Search className="w-4 h-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search Here..."
                  className="text-sm border-none outline-none w-24 md:w-auto"
                />
                <span className="text-sm text-gray-500">2024</span>
                <div className="w-6 h-6 bg-gray-300 rounded-full"></div>
              </div>
            </div>
          </div>

          {/* Form Content */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 md:gap-8">
            {/* Left Column - Form Fields */}
            <div className="space-y-4 md:space-y-6">
              {/* Management System */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Management System
                </label>
                <select
                  value={formData.managementSystem}
                  onChange={(e) => handleInputChange('managementSystem', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="District">District</option>
                  <option value="Province">Province</option>
                  <option value="National">National</option>
                </select>
              </div>

              {/* District */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  District
                </label>
                <input
                  type="text"
                  value={formData.district}
                  onChange={(e) => handleInputChange('district', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter district name"
                />
              </div>

              {/* Total Children */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Total Children
                </label>
                <input
                  type="text"
                  value={formData.totalChildren}
                  onChange={(e) => handleInputChange('totalChildren', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter total children"
                />
              </div>

              {/* Out of School Children */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Out of School Children
                </label>
                <input
                  type="text"
                  value={formData.outOfSchoolChildren}
                  onChange={(e) => handleInputChange('outOfSchoolChildren', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter out of school children"
                />
              </div>

              {/* Gender */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Gender
                </label>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-xs text-gray-600 mb-1">Girls %</label>
                    <input
                      type="text"
                      value={formData.gender.girls}
                      onChange={(e) => handleGenderChange('girls', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="%"
                    />
                  </div>
                  <div>
                    <label className="block text-xs text-gray-600 mb-1">Boys %</label>
                    <input
                      type="text"
                      value={formData.gender.boys}
                      onChange={(e) => handleGenderChange('boys', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="%"
                    />
                  </div>
                </div>
              </div>

              {/* Program Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Program Type
                </label>
                <select
                  value={formData.programType}
                  onChange={(e) => handleInputChange('programType', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="Voucher">Voucher</option>
                  <option value="Formal">Formal</option>
                  <option value="Merged Schools">Merged Schools</option>
                </select>
              </div>

              {/* Date */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Date
                </label>
                <input
                  type="date"
                  value={formData.date}
                  onChange={(e) => handleInputChange('date', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            {/* Right Column - Drop Out Reasons */}
            <div className="space-y-4 md:space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-4">
                  Drop Out Reasons
                </label>
                <div className="space-y-3">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.dropOutReasons.poverty}
                      onChange={(e) => handleDropOutChange('poverty', e.target.checked)}
                      className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Poverty</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.dropOutReasons.distance}
                      onChange={(e) => handleDropOutChange('distance', e.target.checked)}
                      className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Distance</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.dropOutReasons.other}
                      onChange={(e) => handleDropOutChange('other', e.target.checked)}
                      className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Other</span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Success Message */}
          <div className="mt-6 p-3 bg-green-50 border border-green-200 rounded-lg">
            <p className="text-sm text-green-700">Data added Successfully!</p>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mt-6 space-y-4 md:space-y-0">
            <button
              onClick={() => setShowEditModal(true)}
              className="text-[#4A90E2] text-sm font-medium hover:underline"
            >
              Edit Entry
            </button>
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3 w-full md:w-auto">
              <button
                onClick={handleAddEntry}
                className="bg-[#4A90E2] hover:bg-[#2c5aa0] text-white px-4 md:px-6 py-2 rounded-lg font-medium transition-colors duration-200 text-sm md:text-base"
              >
                Add Entry
              </button>
              <button
                onClick={handleReset}
                className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 md:px-6 py-2 rounded-lg font-medium transition-colors duration-200 text-sm md:text-base"
              >
                Reset
              </button>
              <button
                onClick={handleUploadCSV}
                className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 md:px-6 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center space-x-2 text-sm md:text-base"
              >
                <Upload className="w-4 h-4" />
                <span>Upload CSV</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Edit Modal */}
      {showEditModal && (
        <EditDataModal
          isOpen={showEditModal}
          onClose={() => setShowEditModal(false)}
          data={selectedEntry}
        />
      )}
    </div>
  )
}

export default DataManagement
