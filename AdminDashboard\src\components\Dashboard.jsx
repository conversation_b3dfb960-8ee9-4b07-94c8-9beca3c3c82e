import React, { useState } from 'react'
import Sidebar from './Sidebar'
import Header from './Header'
import MainContent from './MainContent'
import DataManagement from './DataManagement'

const Dashboard = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')

  const handleMenuClick = () => {
    setSidebarOpen(!sidebarOpen)
  }

  const handleTabChange = (tab) => {
    setActiveTab(tab)
    setSidebarOpen(false) // Close sidebar on mobile when tab changes
  }

  const renderContent = () => {
    switch (activeTab) {
      case 'data-management':
        return <DataManagement />
      case 'overview':
      default:
        return <MainContent />
    }
  }

  return (
    <div className="flex h-screen bg-[#F8F9FA]">
      {/* Sidebar */}
      <Sidebar
        isOpen={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
        activeTab={activeTab}
        onTabChange={handleTabChange}
      />

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col lg:ml-0">
        <Header onMenuClick={handleMenuClick} />
        {renderContent()}
      </div>
    </div>
  )
}

export default Dashboard
