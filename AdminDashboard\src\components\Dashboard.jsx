import React, { useState } from 'react'
import Sidebar from './Sidebar'
import Header from './Header'
import MainContent from './MainContent'
import DataManagement from './DataManagement'
import Programs from './Programs'
import DistrictsPage from './Districts'

const Dashboard = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')

  const handleMenuClick = () => {
    setSidebarOpen(!sidebarOpen)
  }

  const handleTabChange = (tab) => {
    setActiveTab(tab)
    setSidebarOpen(false) // Close sidebar on mobile when tab changes
  }

  const renderContent = () => {
    switch (activeTab) {
      case 'districts':
        return <DistrictsPage />
      case 'data-management':
        return <DataManagement />
        case 'programs':
        return <Programs />
      case 'overview':
      default:
        return <MainContent />
    }
  }

  return (
    <div className="flex h-screen bg-[#F8F9FA] overflow-hidden">
      {/* Sidebar */}
      <Sidebar
        isOpen={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
        activeTab={activeTab}
        onTabChange={handleTabChange}
      />

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col min-w-0">
        <Header onMenuClick={handleMenuClick} />
        <div className="flex-1 overflow-auto">
          {renderContent()}
        </div>
      </div>
    </div>
  )
}

export default Dashboard
