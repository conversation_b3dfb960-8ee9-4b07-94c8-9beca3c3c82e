import React from 'react'
import { TrendingUp, UserPlus } from 'lucide-react'

const ActionButtons = () => {
  return (
    <div className="flex flex-col sm:flex-row gap-4 justify-center">
      <button className="bg-blue-500 hover:bg-blue-600 text-white px-8 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center space-x-2 shadow-lg">
        <TrendingUp className="w-5 h-5" />
        <span>Track Enrollment</span>
      </button>
      
      <button className="bg-blue-500 hover:bg-blue-600 text-white px-8 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center space-x-2 shadow-lg">
        <UserPlus className="w-5 h-5" />
        <span>Assign Task</span>
      </button>
    </div>
  )
}

export default ActionButtons
