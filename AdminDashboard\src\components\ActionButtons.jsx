import React from 'react'
import { TrendingUp, UserPlus } from 'lucide-react'

const ActionButtons = () => {
  return (
    <div className="flex flex-col sm:flex-row gap-4 justify-center mt-6">
      <button className="bg-[#4A90E2] hover:bg-[#2c5aa0] text-white px-10 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center space-x-2 shadow-sm">
        <TrendingUp className="w-5 h-5" />
        <span>Track Enrollment</span>
      </button>

      <button className="bg-[#4A90E2] hover:bg-[#2c5aa0] text-white px-10 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center space-x-2 shadow-sm">
        <UserPlus className="w-5 h-5" />
        <span>Assign Task</span>
      </button>
    </div>
  )
}

export default ActionButtons
