import React from 'react'
import {
  Home,
  Database,
  Users,
  MapPin,
  FileText,
  Settings,
  LogOut,
  X
} from 'lucide-react'

const Sidebar = ({ isOpen, onClose, activeTab, onTabChange }) => {
  const menuItems = [
    { icon: Home, label: 'Home', key: 'overview' },
    { icon: Database, label: 'Data Management', key: 'data-management' },
    { icon: Users, label: 'Programs', key: 'programs' },
    { icon: MapPin, label: 'Districts', key: 'districts' },
    { icon: FileText, label: 'Reports', key: 'reports' },
    { icon: Users, label: 'Users', key: 'users' },
    { icon: Settings, label: 'Settings', key: 'settings' },
  ]

  return (
    <>
      {/* Mobile Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div className={`
        fixed lg:static inset-y-0 left-0 z-50
        w-64 md:w-56 lg:w-64
        bg-[#2c5aa0] text-white flex flex-col
        transform transition-transform duration-300 ease-in-out
        ${isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
      `}>
        {/* Logo/Header */}
        <div className="p-6 md:p-4 lg:p-6 border-b border-blue-600/30">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
                <span className="text-[#2c5aa0] font-bold text-sm">O</span>
              </div>
              <span className="font-medium text-sm">OOSC Edu App Track</span>
            </div>
            {/* Close button for mobile */}
            <button
              onClick={onClose}
              className="lg:hidden p-1 hover:bg-blue-600/20 rounded"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Navigation Menu */}
        <nav className="flex-1 py-4">
          <ul className="space-y-1">
            {menuItems.map((item, index) => {
              const IconComponent = item.icon
              const isActive = activeTab === item.key
              return (
                <li key={index}>
                  <button
                    onClick={() => onTabChange(item.key)}
                    className={`w-full flex items-center px-6 md:px-4 lg:px-6 py-3 text-sm font-medium transition-colors duration-200 ${
                      isActive
                        ? 'bg-blue-500/20 text-white border-r-3 border-white'
                        : 'text-blue-100/80 hover:bg-blue-500/10 hover:text-white'
                    }`}
                  >
                    <IconComponent className="w-5 h-5 mr-3" />
                    <span className="truncate">{item.label}</span>
                  </button>
                </li>
              )
            })}
          </ul>
        </nav>

        {/* Logout */}
        <div className="p-4 border-t border-blue-600">
          <button
            className="w-full flex items-center px-2 py-2 text-sm font-medium text-blue-100 hover:bg-blue-600 hover:text-white rounded transition-colors duration-200"
          >
            <LogOut className="w-5 h-5 mr-3" />
            <span className="truncate">Log Out</span>
          </button>
        </div>
      </div>
    </>
  )
}

export default Sidebar
