import React from 'react'
import { 
  Home, 
  Database, 
  Users, 
  MapPin, 
  FileText, 
  Settings,
  LogOut 
} from 'lucide-react'

const Sidebar = () => {
  const menuItems = [
    { icon: Home, label: 'Home', active: true },
    { icon: Database, label: 'Data Management', active: false },
    { icon: Users, label: 'Programs', active: false },
    { icon: MapPin, label: 'Districts', active: false },
    { icon: FileText, label: 'Reports', active: false },
    { icon: Users, label: 'Users', active: false },
    { icon: Settings, label: 'Settings', active: false },
  ]

  return (
    <div className="w-64 bg-sidebar text-white flex flex-col">
      {/* Logo/Header */}
      <div className="p-6 border-b border-blue-600">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
            <span className="text-sidebar font-bold text-sm">O</span>
          </div>
          <span className="font-semibold text-sm">OOSC Edu App Track</span>
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="flex-1 py-4">
        <ul className="space-y-1">
          {menuItems.map((item, index) => {
            const IconComponent = item.icon
            return (
              <li key={index}>
                <a
                  href="#"
                  className={`flex items-center px-6 py-3 text-sm font-medium transition-colors duration-200 ${
                    item.active
                      ? 'bg-blue-600 text-white border-r-2 border-white'
                      : 'text-blue-100 hover:bg-blue-600 hover:text-white'
                  }`}
                >
                  <IconComponent className="w-5 h-5 mr-3" />
                  {item.label}
                </a>
              </li>
            )
          })}
        </ul>
      </nav>

      {/* Logout */}
      <div className="p-4 border-t border-blue-600">
        <a
          href="#"
          className="flex items-center px-2 py-2 text-sm font-medium text-blue-100 hover:bg-blue-600 hover:text-white rounded transition-colors duration-200"
        >
          <LogOut className="w-5 h-5 mr-3" />
          Log Out
        </a>
      </div>
    </div>
  )
}

export default Sidebar
